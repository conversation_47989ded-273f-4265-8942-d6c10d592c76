#!/usr/bin/env python3
"""
Test script for the Kraken Live Trading System.
This script tests system components without making real trades.
"""

import sys
import os
import time
from unittest.mock import Mock, patch

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        from config import ConfigManager
        from services.database import DatabaseService
        from services.encryption import EncryptionService
        from services.kraken_client import KrakenClient
        from agents.key_manager import KeyManager
        from agents.market_watcher import MarketWatcher
        from agents.trader import Trader
        from agents.risk_manager import RiskManager
        from agents.audit_agent import AuditAgent
        from services.dashboard import TradingDashboard
        from utils.constants import MAJOR_PAIRS, US_RESTRICTED_ASSETS
        from utils.helpers import format_currency, calculate_percentage_change
        from utils.validators import TradingValidator
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_database():
    """Test database functionality."""
    print("\n🗄️ Testing database...")
    
    try:
        from services.database import DatabaseService
        
        # Create test database
        os.makedirs("data", exist_ok=True)
        db = DatabaseService("data/test_trading_system.db")
        
        # Test adding API key
        success = db.add_api_key("test_key_1", "Test Key", "starter")
        assert success, "Failed to add API key"
        
        # Test getting API keys
        keys = db.get_active_api_keys()
        assert len(keys) > 0, "No API keys found"
        
        # Test recording trade
        trade_data = {
            'trade_id': 'test_trade_1',
            'key_id': 'test_key_1',
            'pair': 'BTCUSD',
            'side': 'buy',
            'amount': 0.001,
            'price': 50000,
            'status': 'executed',
            'strategy': 'scalping'
        }
        
        success = db.record_trade(trade_data)
        assert success, "Failed to record trade"
        
        # Test market data
        db.update_market_data('BTCUSD', 50000, 1000000, 0.02, 0.05)
        
        # Test system state
        db.set_system_state('test_key', {'value': 'test'})
        value = db.get_system_state('test_key')
        assert value == {'value': 'test'}, "System state test failed"
        
        # Cleanup
        os.remove("data/test_trading_system.db")
        
        print("✅ Database tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_encryption():
    """Test encryption functionality."""
    print("\n🔐 Testing encryption...")
    
    try:
        from services.encryption import EncryptionService
        
        encryption = EncryptionService()
        encryption.set_master_password("test_password_123")
        
        # Test API key validation
        is_valid = encryption.validate_api_key_format(
            "test_api_key_1234567890123456789012345678901234567890123456",
            "dGVzdF9hcGlfc2VjcmV0XzEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MA=="
        )
        assert is_valid, f"API key validation failed"
        
        # Test encryption/decryption
        test_keys = [
            {
                'api_key': 'test_key_1',
                'api_secret': 'test_secret_1',
                'alias': 'Test Key 1',
                'tier': 'starter'
            }
        ]
        
        encrypted = encryption.encrypt_api_keys(test_keys)
        assert encrypted, "Encryption failed"
        
        decrypted = encryption.decrypt_api_keys(encrypted)
        assert decrypted == test_keys, "Decryption failed"
        
        print("✅ Encryption tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False

def test_validators():
    """Test validation functions."""
    print("\n✅ Testing validators...")
    
    try:
        from utils.validators import TradingValidator
        
        # Test trading pair validation
        valid, msg = TradingValidator.validate_trading_pair("BTCUSD")
        assert valid, f"Trading pair validation failed: {msg}"
        
        # Test invalid pair
        valid, msg = TradingValidator.validate_trading_pair("INVALIDPAIR")
        assert not valid, "Should reject invalid trading pair"
        
        # Test trade amount
        valid, msg = TradingValidator.validate_trade_amount(0.001, "BTCUSD")
        assert valid, f"Trade amount validation failed: {msg}"
        
        # Test price validation
        valid, msg = TradingValidator.validate_price(50000, "BTCUSD")
        assert valid, f"Price validation failed: {msg}"
        
        # Test risk profile
        valid, msg = TradingValidator.validate_risk_profile("balanced")
        assert valid, f"Risk profile validation failed: {msg}"
        
        print("✅ Validator tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Validator test failed: {e}")
        return False

def test_helpers():
    """Test helper functions."""
    print("\n🛠️ Testing helpers...")
    
    try:
        from utils.helpers import format_currency, calculate_percentage_change, calculate_rsi
        
        # Test currency formatting
        formatted = format_currency(1234.56, "USD")
        assert "$1.2K" in formatted or "$1234.56" in formatted, f"Currency formatting failed: {formatted}"
        
        # Test percentage change
        change = calculate_percentage_change(100, 110)
        assert abs(change - 10.0) < 0.01, f"Percentage change calculation failed: {change}"
        
        # Test RSI calculation
        prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 111, 110, 112, 114, 113]
        rsi = calculate_rsi(prices)
        assert 0 <= rsi <= 100, f"RSI calculation failed: {rsi}"
        
        print("✅ Helper tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Helper test failed: {e}")
        return False

def test_constants():
    """Test constants and configuration."""
    print("\n📋 Testing constants...")
    
    try:
        from utils.constants import MAJOR_PAIRS, US_RESTRICTED_ASSETS, RISK_PROFILES, STRATEGIES
        
        # Test that constants are properly defined
        assert len(MAJOR_PAIRS) > 0, "No major pairs defined"
        assert len(US_RESTRICTED_ASSETS) > 0, "No restricted assets defined"
        assert len(RISK_PROFILES) > 0, "No risk profiles defined"
        assert len(STRATEGIES) > 0, "No strategies defined"
        
        # Test specific values
        assert "BTCUSD" in MAJOR_PAIRS, "BTCUSD not in major pairs"
        assert "balanced" in RISK_PROFILES, "Balanced risk profile not defined"
        assert "scalping" in STRATEGIES, "Scalping strategy not defined"
        
        print("✅ Constants tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Constants test failed: {e}")
        return False

def test_mock_trading_flow():
    """Test trading flow with mocked components."""
    print("\n🔄 Testing mock trading flow...")
    
    try:
        from services.database import DatabaseService
        from agents.key_manager import KeyManager
        from agents.market_watcher import MarketWatcher
        from agents.risk_manager import RiskManager
        from agents.trader import Trader
        
        # Create test database
        os.makedirs("data", exist_ok=True)
        db = DatabaseService("data/test_flow.db")
        
        # Create key manager with mock client
        key_manager = KeyManager(db)
        
        # Mock a successful API key addition
        with patch.object(key_manager, 'add_api_key', return_value=True):
            success = key_manager.add_api_key("test_key", "test_secret", "Test", "starter")
            assert success, "Failed to add mock API key"
        
        # Create market watcher
        market_watcher = MarketWatcher(key_manager, db)
        
        # Create risk manager
        risk_manager = RiskManager(key_manager, db)
        
        # Create trader
        trader_config = {
            'max_trades_per_minute': 5,
            'max_dollar_per_trade': 100,
            'risk_profile': 'balanced',
            'strategy': 'scalping'
        }
        
        trader = Trader("test_trader", key_manager, db, "test_key", trader_config)
        
        # Test trader analysis (should not fail)
        mock_market_data = {
            'BTCUSD': {
                'price': 50000,
                'volume_24h': 1000000,
                'change_24h': 0.02,
                'volatility': 0.05
            }
        }
        
        recommendation = trader.analyze_opportunity(mock_market_data, 'BTCUSD')
        assert 'action' in recommendation, "Trader analysis failed"
        
        # Cleanup
        os.remove("data/test_flow.db")
        
        print("✅ Mock trading flow tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Mock trading flow test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Kraken Trading System - Component Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_constants,
        test_helpers,
        test_validators,
        test_encryption,
        test_database,
        test_mock_trading_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System components are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
